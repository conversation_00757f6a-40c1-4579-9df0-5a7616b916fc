import 'package:flutter/material.dart';
import '../constant/AppColor.dart';

class GradientContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final BoxShape shape;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const GradientContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.shape = BoxShape.rectangle,
    this.width,
    this.height,
    this.alignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      decoration: AppColor.primaryGradientDecoration(
        borderRadius: borderRadius,
        shape: shape,
      ),
      child: child,
    );
  }
}

class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;
  final double? width;
  final double? height;

  const GradientButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.padding,
    this.borderRadius,
    this.textStyle,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: borderRadius ?? BorderRadius.circular(10),
      child: GradientContainer(
        width: width,
        height: height,
        padding: padding ?? EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        borderRadius: borderRadius ?? BorderRadius.circular(10),
        child: Center(
          child: Text(
            text,
            style: textStyle ?? TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class GradientIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final BoxShape shape;

  const GradientIcon({
    Key? key,
    required this.icon,
    this.size,
    this.padding,
    this.borderRadius,
    this.shape = BoxShape.rectangle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GradientContainer(
      padding: padding ?? EdgeInsets.all(8),
      borderRadius: borderRadius,
      shape: shape,
      child: Icon(
        icon,
        size: size,
        color: Colors.white,
      ),
    );
  }
}