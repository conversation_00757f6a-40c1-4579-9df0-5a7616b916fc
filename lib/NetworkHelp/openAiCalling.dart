import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../Model/openAIModel.dart';
import '../constant/Appkey.dart';

class OpenAiCalling {

  static Future<String> sentImageApi(File image) async {
    try {
      String apiEndpoint = 'https://api.openai.com/v1/chat/completions';
      final headers = {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      };
      final bytes = await image.readAsBytes();
      final base64Image = base64Encode(bytes);
      final parameters = {
        'model': 'gpt-4o-mini',
        'messages': [
          {
            "role": "system",
            "content": "You are a Food Tracker expert. Provide answers in English.",
          },
          {
            "role": "assistant",
            "content": "Give me this Food Image calorie,protein,Carbohydrates,fats and i have one image many food then give me only Total Estimated Nutritional Values not give any String".tr,
          },
          {
            'role': 'user',
            'content': [
              {
                'type': 'image_url',
                'image_url': {'url': "data:image/jpeg;base64,$base64Image"},
              },
            ],
          },
        ],
        'max_tokens': 1000,
      };
      final response = await http.post(
        Uri.parse(apiEndpoint),
        headers: headers,
        body: jsonEncode(parameters),
      );

      if (response.statusCode == 200) {
        final responseBody = utf8.decode(response.bodyBytes);
        final decodedJson = jsonDecode(responseBody);
        OpenAiModel data = OpenAiModel.fromJson(decodedJson);
        return data.choices!.first.message!.content.toString();
      } else {
        if (kDebugMode) {
          print(response.statusCode);
          print(response.body);
        }
        return "Something Went Wrong";
      }
    } catch (e) {
      if (kDebugMode) {
        print("error is====> $e");
      }
      return "Something Went Wrong";
    }
  }
}
