
import 'package:flutter/material.dart';

class AppColor{

  static Color grey = Colors.grey;
  static Color white = Colors.white;
  static Color black = Colors.black;
  
  // New gradient colors
  static Color primaryGradientStart = Color(0xFF6AEED0);
  static Color primaryGradientSecond = Color(0xFF42E6CC);
  static Color primaryGradientThird = Color(0xFF01BC40);
  static Color primaryGradientEnd = Color(0xFFA3FEC2);
  
  // Primary gradient definition
  static LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.topRight,
    colors: [
      Color(0xFF6AEED0), // 0%
      Color(0xFF42E6CC), // 26%
      Color(0xFF01BC40), // 50%
      Color(0xFFA3FEC2), // 100%
    ],
    stops: [
      0.0,   // 0%
      0.26,  // 26%
      0.5,   // 50%
      1.0,   // 100%
    ],
  );
  
  // Helper method to get gradient as decoration
  static BoxDecoration primaryGradientDecoration({
    BorderRadius? borderRadius,
    BoxShape shape = BoxShape.rectangle,
  }) {
    return BoxDecoration(
      gradient: primaryGradient,
      borderRadius: shape == BoxShape.rectangle ? borderRadius : null,
      shape: shape,
    );
  }
}