abstract class Routes {
  Routes._();
  static const splashView = Paths.splashView;
  static const onBoardingView = Paths.onBoardingView;
  static const signUpView = Paths.signUpView;
  static const leadingView = Paths.leadingView;
  static const homeView = Paths.homeView;
  static const scanFoodView = Paths.scanFoodView;
  static const analyticsView = Paths.analyticsView;
  static const settingView = Paths.settingView;
  static const adjustGoalsView = Paths.adjustGoalsView;
  static const personalDetailsView = Paths.personalDetailsView;
  static const scanCalorieView = Paths.scanCalorieView;
  static const historyView = Paths.historyView;
  static const premiumView = Paths.premiumView;
  static const chatView = Paths.chatView;
  static const chatHistoryView = Paths.chatHistoryView;
  static const localFoodView = Paths.localFoodView;
  static const languageView = Paths.languageView;
}

abstract class Paths {
  Paths._();
  static const splashView = '/SplashView';
  static const onBoardingView = '/OnBoardingView';
  static const signUpView = '/SignUpView';
  static const leadingView = '/LeadingView';
  static const homeView = '/HomeView';
  static const scanFoodView = '/ScanFoodView';
  static const analyticsView = '/AnalyticsView';
  static const settingView = '/SettingView';
  static const adjustGoalsView = '/AdjustGoalsView';
  static const personalDetailsView = '/PersonalDetailsView';
  static const scanCalorieView = '/ScanCalorieView';
  static const historyView = '/HistoryView';
  static const premiumView = '/PremiumView';
  static const chatView = '/ChatView';
  static const chatHistoryView = '/ChatHistoryView';
  static const localFoodView = '/LocalFoodView';
  static const languageView = '/LanguageView';

}
