import 'dart:convert';
import 'dart:io';
import 'package:dart_openai/dart_openai.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:foodcalorietracker/Model/MainChatModel.dart';
import 'package:foodcalorietracker/Model/SubchatModel.dart';
import 'package:foodcalorietracker/constant/DatabaseHelper.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_to_text.dart';
import '../../MainController.dart';
import '../../Model/ChatModel.dart';
import '../../Model/openAIModel.dart';
import '../../constant/Appkey.dart';
import '../../constant/FontFamily.dart';
import '../../services/UsageTrackingService.dart';

class ChatController extends GetxController {
  Map<String,dynamic>? argument = Get.arguments;
  bool recording = false;
  TextEditingController controller = TextEditingController();
  ScrollController scrollController = ScrollController();
  TextEditingController feedController = TextEditingController();
  List<ChatModel> messages = [];
  final ImagePicker _picker = ImagePicker();
  String speechToText = "";
  final SpeechToText speech = SpeechToText();
  final dbHelper = DatabaseHelper();
  File? imagePath;
  bool isStreamedText = false;
  bool isTyping = false;
  String streamedText = "";
  bool speechEnabled = false;
  int mainChatId = 0;
  String selectedReason = "Wrong answer";
  bool isMainChat = true;


  @override
  void onInit() {
    OpenAI.apiKey = apiKey;

    // TODO: implement onInit

    _initSpeech();

    super.onInit();
    if(argument != null)
    {
      if(argument!['mainChatId'] != null)
      {
        isMainChat = false;
        mainChatId = argument!['mainChatId'];
        getHistory();
      }else{
        imagePath = argument!['image'];
      }
    }
  }
  getHistory()
  async {
    List<SubChatModel> data = await dbHelper.getSubChat(mainChatId);
    for (var element in data) {
      messages.insert(0, ChatModel(true,element.question,element.image,false));
      messages.insert(0, ChatModel(false,element.answer,element.image,true));
    }
    update();
  }

  void _initSpeech() async {

    speechEnabled = await speech.initialize();
    update();
  }

  void sendMsg({required String text}) async {
    try {
      if (text.isNotEmpty) {
        text = text.trim();
        
        // Check if user can use chat feature
        bool canUse = await UsageTrackingService.canUseChatFeature();
        if (!canUse) {
          controller.clear();
          FocusManager.instance.primaryFocus?.unfocus();
          // Add limitation message
          messages.insert(0, ChatModel(true, text, imagePath?.path, false));
          messages.insert(0, ChatModel(false, "You've reached your free chat limit. Upgrade to premium for unlimited AI conversations!", imagePath?.path, true));
          update();
          // Show upgrade dialog after a short delay
          Future.delayed(Duration(seconds: 1), () {
            UsageTrackingService.showUpgradeDialog();
          });
          return;
        }
        
        // Increment usage count (only for first message in conversation)
        if(messages.isEmpty) {
          await UsageTrackingService.incrementChatUsage();
        }
        
        controller.clear();
        FocusManager.instance.primaryFocus?.unfocus();
        isTyping = true;
        if(messages.isEmpty)
        {
          isMainChat = true;
        }else{
          isMainChat = false;
        }
        messages.insert(0, ChatModel(true, text, imagePath?.path,false));
        isStreamedText = true;
        messages.insert(0, ChatModel(false, "", imagePath?.path,false));
        update();

        if (imagePath != null) {
          File imageDemo = imagePath!;
          imagePath = null;
          final bytes = await imageDemo.readAsBytes();
          final base64Image = base64Encode(bytes);
          update();
          final headers = {
            'Authorization': 'Bearer $apiKey',
            'Content-Type': 'application/json',
          };
          final parameters = {
            'model': "gpt-4o",
            'messages': [
              {
                'role': 'system',
                'content': "You are food expert Ai",
                // Simulate the assistant's prior context or guidance
              },
              {
                'role': 'assistant',
                'content':
                "give me response in ${Get.find<MainController>().language} language",
                // Simulate the assistant's prior context or guidance
              },
              {
                'role': 'user',
                'content': [
                  {'type': 'text', 'text': text},
                  {
                    'type': 'image_url',
                    'image_url': {'url': "data:image/jpeg;base64,$base64Image"},
                  },
                ],
              },
            ],
            'max_tokens': 500,
          };

          final client = http.Client();
          String apiEndpoint = 'https://api.openai.com/v1/chat/completions';
          final request = http.Request('POST', Uri.parse(apiEndpoint))
            ..headers.addAll(headers)
            ..body = jsonEncode(parameters);

          final streamedResponse = await client.send(request);
          final response = await http.Response.fromStream(streamedResponse);

          if (response.statusCode == 200) {
            final decodedJson = jsonDecode(response.body);
            OpenAiModel data = OpenAiModel.fromJson(decodedJson);
            messages.first = ChatModel(
                false,
                data.choices!.first.message!.content.toString(),
                imageDemo.path,true
            );
            if (isMainChat) {
              mainChatId = await dbHelper.insertMainChatModel(
                MainChatModel(
                  question: text,
                  answer:  data.choices!.first.message!.content.toString(),
                  date: DateTime.now().toString(),
                ),
              );
              await dbHelper.insertSubChatModel(
                SubChatModel(
                    question: text,
                    answer:  data.choices!.first.message!.content.toString(),
                    date: DateTime.now().toString(),
                    mainCharId: mainChatId,
                    image: imageDemo.path
                ),
              );
            } else {
              await dbHelper.insertSubChatModel(
                SubChatModel(
                    question: text,
                    answer:  data.choices!.first.message!.content.toString(),
                    date: DateTime.now().toString(),
                    mainCharId: mainChatId,
                    image: imageDemo.path
                ),
              );
            }
            streamedText = "";
            isStreamedText = false;
            update();
          } else {

            messages.first = ChatModel(
                false,
                "Something Went Wrong",
                imageDemo.path,
                true
            );
            streamedText = "";
            isStreamedText = false;
            update();
          }
        } else {
          Stream<OpenAIStreamChatCompletionModel> chatStream = OpenAI
              .instance
              .chat
              .createStream(
            model: "gpt-4o-mini",
            // Or gpt-3.5-turbo, gpt-4, etc.
            messages: [
              OpenAIChatCompletionChoiceMessageModel(
                role: OpenAIChatMessageRole.user,
                content: [
                  OpenAIChatCompletionChoiceMessageContentItemModel.text(
                    text,
                  ),
                ],
              ),
              OpenAIChatCompletionChoiceMessageModel(
                role: OpenAIChatMessageRole.system,
                content: [
                  OpenAIChatCompletionChoiceMessageContentItemModel.text(
                    "You are a Food Tracker expert. Provide answers in ${Get.find<MainController>().language}.",
                  ),
                ],
              ),
            ],
            temperature: 0.5,
            topP: 1,
            seed: 42,
            stop: ["###"],
            n: 1,
          );
          chatStream.listen(
                (event) {
              final choice = event.choices.first;
              final content = choice.delta.content;
              if (content != null) {
                streamedText = streamedText + content[0]!.text.toString();
                update();
              } // ...
            },
            onDone: () async {
              imagePath = null;
              messages.first = ChatModel(
                  false,
                  streamedText.toString(),
                  imagePath?.path,
                  false
              );
              if (isMainChat) {
                mainChatId = await dbHelper.insertMainChatModel(
                  MainChatModel(
                    question: text,
                    answer: streamedText.toString(),
                    date: DateTime.now().toString(),
                  ),
                );
                await dbHelper.insertSubChatModel(
                  SubChatModel(
                    question: text,
                    answer: streamedText.toString(),
                    date: DateTime.now().toString(),
                    mainCharId: mainChatId,
                  ),
                );
              } else {
                await dbHelper.insertSubChatModel(
                  SubChatModel(
                    question: text,
                    answer: streamedText.toString(),
                    date: DateTime.now().toString(),
                    mainCharId: mainChatId,
                  ),
                );
              }
              streamedText = "";
              isStreamedText = false;
              update();
            },
          );
        }

        await Future.delayed(const Duration(milliseconds: 100));
        scrollController.animateTo(
          0.0,
          duration: const Duration(seconds: 1),
          curve: Curves.easeOut,
        );
      }
    } catch (err) {
      /// Show Error Toast USER
      Fluttertoast.showToast(
        msg: "Hmm...something seems to have gone wrong.",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        fontSize: 12.0,
      );

      /// Print Error debug mode
      if (kDebugMode) {
        print("ERROR $err");
      }
      isTyping = false;
      imagePath = null;
      streamedText = "";
      isStreamedText = false;
      messages.removeAt(0);
      update();
    }
  }

  takeImage(ImageSource source, BuildContext context) async {
    XFile? image = await _picker.pickImage(source: source);
    if (image != null) {
      File imagePath = File(image.path);
      update();
      await cropImage(imagePath, context);
    }
  }

  Future<void> cropImage(final image, BuildContext context) async {
    update();
    if (image != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: image.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Cropper'.tr,
            toolbarColor: context.theme.focusColor,
            toolbarWidgetColor: context.theme.hintColor,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false,
          ),
          IOSUiSettings(title: 'Cropper'.tr),
        ],
      );
      if (croppedFile != null) {
        imagePath = File(croppedFile.path);
        update();
      } else {
        update();
      }
    }
  }

  @override
  void onClose() {
    controller.dispose();
    scrollController.dispose();
    // TODO: implement onClose
    super.onClose();
  }

  void startListening() async {
    await speech.listen(onResult: _onSpeechResult);
    recording = true;
    update();
  }

  void stopListening(BuildContext context) async {
    await speech.stop();
    recording = false;
    sendMsg(text: speechToText);
    update();
  }

  void _onSpeechResult(SpeechRecognitionResult result) {
    speechToText = result.recognizedWords;
    update();
  }

  removeImage() {
    imagePath = null;
    update();
  }
  showFeedbackSheet(BuildContext context,int index)  {

    showModalBottomSheet(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            left: 20,
            right: 20,
            top: 10,
            bottom: MediaQuery.of(context).viewInsets.bottom + 10,
          ),
          child: GetBuilder<ChatController>(builder: (controller) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(width: 48), // balance left-right IconButton

                    Text(
                      'Help us do better',
                      style: context.textTheme.headlineMedium,
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context,); // User dismissed manually
                      },
                      icon: Icon(Icons.close, color: context.theme.primaryColor),
                    ),
                  ],
                ),
                RadioListTile<String>(
                  title: Text('Wrong answer',
                      style: context.textTheme.titleSmall),
                  value: 'Wrong answer',
                  groupValue: selectedReason,
                  activeColor: context.theme.focusColor,
                  onChanged: (value) {
                    selectedReason = value!;
                    update();
                  },
                ),
                RadioListTile<String>(
                  title: Text('Unsatisfactory explanation',
                      style: context.textTheme.titleSmall),
                  value: 'Unsatisfactory explanation',
                  groupValue: selectedReason,
                  activeColor: context.theme.focusColor,
                  onChanged: (value) {
                    selectedReason = value!;
                    update();
                  },
                ),
                TextField(
                  controller: feedController,
                  style:TextStyle(color: Colors.black),
                  cursorColor: context.theme.focusColor,
                  decoration: InputDecoration(
                    hintText: 'Correction or advice...',
                    filled: true,
                    fillColor: Colors.grey[300],
                    hintStyle: TextStyle(color: Colors.black),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 20),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    Fluttertoast.showToast(msg: 'Thanks');
                    selectedReason = "Wrong answer";
                    feedController.clear();
                    messages.removeRange(index, index+2);
                    update();
                    // Return true on submit
                  },
                  child: Container(
                    margin: EdgeInsets.all(10),
                    alignment: Alignment.center,
                    height: 50,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: context.theme.focusColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      "Submit",
                      style: TextStyle(
                          fontFamily: poppins,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: Colors.white),
                    ),
                  ),
                )
              ],
            );
          }),
        );
      },
    );
  }
}
