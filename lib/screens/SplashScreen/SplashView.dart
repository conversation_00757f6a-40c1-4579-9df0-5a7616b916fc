import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../MainController.dart';
import '../../constant/AppAssets.dart';
import '../../routes/app_routes.dart';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  _navigateToNextScreen() {
    Timer(const Duration(seconds: 3), () {
      final MainController controller = Get.find<MainController>();
      
      // Navigate based on login status
      if (controller.isLogin) {
        Get.offAllNamed(Routes.leadingView);
      } else {
        Get.offAllNamed(Routes.onBoardingView);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
     return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/images/splash.png")),
        ),
      ),
    );
  }
}