import 'package:flutter/material.dart';
import 'package:foodcalorietracker/screens/SignUpScreens/SignUpController.dart';
import 'package:get/get.dart';

class SignUpView extends GetView<SignUpController> {
  const SignUpView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      body: Padding(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          bottom: MediaQuery.of(context).padding.bottom + 10,
          right: 10,
          left: 10,
        ),
        child: GetBuilder<SignUpController>(
          builder: (controller) {
            return Column(
              children: [
                // Back Button Row
                Row(
                  children: [
                    if (controller.selectedView > 0)
                      GestureDetector(
                        onTap: () {
                          controller.onPreviousView();
                        },
                        child: Container(
                          padding: EdgeInsets.all(8),
                          child: Icon(
                            Icons.arrow_back_ios,
                            color: context.theme.primaryColor,
                            size: 24,
                          ),
                        ),
                      ),
                    if (controller.selectedView == 0)
                      GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          padding: EdgeInsets.all(8),
                          child: Icon(
                            Icons.arrow_back_ios,
                            color: context.theme.primaryColor,
                            size: 24,
                          ),
                        ),
                      ),
                  ],
                ),
                // Progress Bar
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Step ${controller.selectedView + 1} of ${controller.screens.length}",
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.theme.primaryColor.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                          Text(
                            "${((controller.selectedView + 1) / controller.screens.length * 100).toInt()}%",
                            style: context.textTheme.titleSmall?.copyWith(
                              color: context.theme.primaryColor.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: LinearProgressIndicator(
                          value:
                              (controller.selectedView + 1) /
                              controller.screens.length,
                          backgroundColor: context.theme.primaryColor
                              .withValues(alpha: 0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            context.theme.focusColor,
                          ),
                          minHeight: 6,
                        ),
                      ),
                    ],
                  ),
                ),
                // Main Screen Content
                Expanded(child: controller.screens[controller.selectedView]),
              ],
            );
          },
        ),
      ),
    );
  }
}
