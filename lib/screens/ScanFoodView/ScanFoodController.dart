import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import '../../main.dart';
import '../../routes/app_routes.dart';
import '../../services/UsageTrackingService.dart';

class ScanFoodController extends GetxController {
  late CameraController cameraController;
  String isIdentify = "BreakFast";
  File? imagePath;
  bool isLoading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  Future<void> onInit() async {
    // TODO: implement onInit
    super.onInit();

    cameraController = CameraController(cameras[0], ResolutionPreset.max);

    try {
      await cameraController.initialize(); // Wait for initialization
      await cameraController.lockCaptureOrientation(
        DeviceOrientation.portraitUp,
      ); // Now it's safe
      update();
    } catch (e) {
      if (e is CameraException) {
        if (kDebugMode) {
          print("Camera error: ${e.description}");
          print("Error code: ${e.code}");
        }

        if (e.code == 'CameraAccessDenied') {
          // Handle permission denial
        } else {
          // Handle other errors
        }
      }
    }
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    cameraController.dispose();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    cameraController.dispose();
  }

  takeImage(ImageSource source, BuildContext context) async {
    XFile? image = await _picker.pickImage(source: source);
    if (image != null) {
      imagePath = File(image.path);
      update();
      await cropImage(imagePath, context);
    }
  }

  onTackImageCamera(BuildContext context) async {
    isLoading = true;
    update();
    XFile imageFile = await cameraController.takePicture();
    File originalFile = File(imageFile.path);
    cropImage(originalFile, context);
  }

  Future<void> cropImage(final image, BuildContext context) async {
    isLoading = false;

    update();
    if (image != null) {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: image.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Cropper'.tr,
            toolbarColor: context.theme.focusColor,
            toolbarWidgetColor: context.theme.hintColor,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false,
          ),
          IOSUiSettings(title: 'Cropper'.tr),
        ],
      );
      if (croppedFile != null) {
        File image = File(croppedFile.path);
        
        // Check if user can use scan feature
        bool canUse = await UsageTrackingService.canUseScanFeature();
        if (!canUse) {
          // Show upgrade dialog
          UsageTrackingService.showUpgradeDialog();
          isLoading = false;
          update();
          return;
        }
        
        // Navigate to scan calorie view
        Get.toNamed(
          Routes.scanCalorieView,
          arguments: {'image': image, 'type': isIdentify},
        );
      } else {
        isLoading = false;
        update();
      }
    }
  }
  onChangeIdentify(String value) {
    isIdentify = value;
    update();
  }
}
