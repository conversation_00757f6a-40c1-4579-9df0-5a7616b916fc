import 'package:foodcalorietracker/SharePrefHelper/SharePref.dart';
import 'package:foodcalorietracker/SharePrefHelper/SharePrefKey.dart';
import 'package:foodcalorietracker/constant/Appkey.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';

class UsageTrackingService {
  static Future<bool> canUseScanFeature() async {
    // Check if user is premium
    bool isPremium = await SharedPref.readBool(SharePrefKey.isPremium) ?? false;
    if (isPremium) {
      // Check if premium is still valid by date
      String premiumDate = await SharedPref.readString(SharePrefKey.premiumDate);
      if (premiumDate.isNotEmpty) {
        DateTime fin = DateTime.parse(premiumDate);
        DateTime date = DateTime.now();
        DateTime time = DateTime(date.year, date.month, date.day);
        if (time.compareTo(fin) < 0) {
          return true; // Premium is still valid
        }
      }
    }
    
    // Check free usage limit
    int currentUsage = await SharedPref.readInt(SharePrefKey.scanLimit) ?? 0;
    return currentUsage < scanLimit;
  }
  
  static Future<bool> canUseChatFeature() async {
    // Check if user is premium
    bool isPremium = await SharedPref.readBool(SharePrefKey.isPremium) ?? false;
    if (isPremium) {
      // Check if premium is still valid by date
      String premiumDate = await SharedPref.readString(SharePrefKey.premiumDate);
      if (premiumDate.isNotEmpty) {
        DateTime fin = DateTime.parse(premiumDate);
        DateTime date = DateTime.now();
        DateTime time = DateTime(date.year, date.month, date.day);
        if (time.compareTo(fin) < 0) {
          return true; // Premium is still valid
        }
      }
    }
    
    // Check free usage limit
    int currentUsage = await SharedPref.readInt(SharePrefKey.chatLimit) ?? 0;
    return currentUsage < chatLimit;
  }
  
  static Future<void> incrementScanUsage() async {
    int currentUsage = await SharedPref.readInt(SharePrefKey.scanLimit) ?? 0;
    await SharedPref.saveInt(SharePrefKey.scanLimit, currentUsage + 1);
  }
  
  static Future<void> incrementChatUsage() async {
    int currentUsage = await SharedPref.readInt(SharePrefKey.chatLimit) ?? 0;
    await SharedPref.saveInt(SharePrefKey.chatLimit, currentUsage + 1);
  }
  
  static Future<int> getScanUsageCount() async {
    return await SharedPref.readInt(SharePrefKey.scanLimit) ?? 0;
  }
  
  static Future<int> getChatUsageCount() async {
    return await SharedPref.readInt(SharePrefKey.chatLimit) ?? 0;
  }
  
  static void showUpgradeDialog() {
    Get.toNamed(Routes.premiumView);
  }
  
  static Future<void> resetUsageLimits() async {
    await SharedPref.saveInt(SharePrefKey.scanLimit, 0);
    await SharedPref.saveInt(SharePrefKey.chatLimit, 0);
  }
}